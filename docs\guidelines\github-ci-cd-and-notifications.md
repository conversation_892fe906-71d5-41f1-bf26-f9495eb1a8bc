# GitHub CI/CD và Notification System

## Tổng quan

Hệ thống CI/CD bao gồm:
1. **CI Build Pipeline** - Tự động build và test frontend/backend
2. **Requirements Change Notification** - Thông báo Lark khi có thay đổi yêu cầu nghiệp vụ

## 1. CI Build Pipeline (`ci-build.yml`)

### Chức năng
- Tự động chạy khi có push/PR tới các branch chính
- Build và test cả frontend (NextJS/TypeScript) và backend (Java Spring Boot)
- Hiển thị trạng thái build tổng quát

### Triggers
- Push tới `main`, `develop`, `feature/*`
- Pull Request tới `main`, `develop`

### Jobs
1. **build-backend**: Build Java Spring Boot project với Maven
2. **build-frontend**: Build NextJS project với pnpm
3. **build-status**: Tó<PERSON> tắt kết quả build

### Y<PERSON><PERSON> cầu hệ thống
- Java JDK 21
- Node.js 20
- pnpm 8
- Maven cache để tăng tốc build

## 2. Requirements Change Notification (`requirements-change-notification.yml`)

### Chức năng
- Gửi thông báo Lark khi có GitHub issue mới với label `yêu-cầu-nghiệp-vụ`
- Tích hợp với workflow "Đánh giá thay đổi yêu cầu nghiệp vụ" đã có

### Trigger
- Issue được tạo với label `yêu-cầu-nghiệp-vụ`

### Cấu hình Secret và Variables
Cần cấu hình trong GitHub repository:

**Secrets** (`Repository Settings > Secrets and variables > Actions > Secrets`):
```text
Name: LARK_CHAT_GROUP_NOTIFICATION
Value: [Lark Bot Webhook URL]
```text

**Variables** (`Repository Settings > Secrets and variables > Actions > Variables`):
```text
Name: PROJECT_CODE
Value: [Mã dự án, ví dụ: CMS, ERP, v.v.]
```text

### Định dạng thông báo
Tiêu đề thông báo sẽ có format:
```text
[OSP][<PROJECT_CODE>] [Requirements] <Tên Issue> [Thay đổi mới]
```text

Ví dụ:
```text
[OSP][CMS] [Requirements] Cập nhật quy trình đăng ký người dùng [Thay đổi mới]
```text

### Nội dung thông báo
- Link tới GitHub issue
- Thông tin người tạo và thời gian
- Danh sách hành động cần thực hiện:
  - Đánh giá tác động thay đổi
  - Phân tích rủi ro
  - Cập nhật documentation
  - Review implementation plan

### Kết nối với workflow hiện tại
Workflow này hoạt động cùng với `requirements-change-review.yml`:
1. Khi có thay đổi trong `docs/requirements/` → Tự động tạo issue
2. Issue có label `yêu-cầu-nghiệp-vụ` → Tự động gửi thông báo Lark

## Troubleshooting

### CI Build Errors
- **Maven build fail**: Kiểm tra Java version và dependencies trong `pom.xml`
- **pnpm build fail**: Kiểm tra Node.js version và `package.json`
- **Cache issues**: Xóa cache trong GitHub Actions settings

### Lark Notification Issues
- **Secret not configured**: Đảm bảo `LARK_CHAT_GROUP_NOTIFICATION` được cấu hình đúng
- **PROJECT_CODE not set**: Đảm bảo variable `PROJECT_CODE` được cấu hình (sẽ hiển thị `[OSP][UNKNOWN]` nếu chưa cấu hình)
- **Webhook failed**: Kiểm tra URL webhook và quyền truy cập Lark
- **Issue không có label**: Đảm bảo workflow `requirements-change-review.yml` gắn đúng label

## Best Practices

1. **CI Build**:
   - Sử dụng cache để tăng tốc build
   - Chạy test song song với build
   - Monitor build time và optimize khi cần

2. **Notifications**:
   - Đảm bảo timezone correct (GMT+7)
   - Cấu hình PROJECT_CODE để có thông báo rõ ràng (ví dụ: CMS, ERP, HRMS)
   - Format thông báo rõ ràng với OSP prefix và có action items
   - Test webhook trước khi deploy production

## Tags
`ci-cd`, `github-actions`, `build-automation`, `lark-notification`, `requirements-management`
