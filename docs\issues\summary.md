# Tóm tắt Issues

## Danh sách các lỗi đã giải quyết

| Tên lỗi                           | <PERSON><PERSON> tả tóm tắt                                                                                                    | Tag                                                                                                               |
| --------------------------------- | ---------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| Duplicate CI Build Jobs           | Sửa lỗi CI jobs chạy duplicate do push và PR events trigger cùng lúc, gây lãng phí tài nguyên                    | ci-cd, duplicate-jobs, github-actions, workflow-optimization, resource-management                                 |
| Smart CI Build Logic Enhancement  | Cải tiến logic CI build với điều kiện target branch và label control để tối ưu resource                          | ci-cd, smart-logic, label-control, target-branch, resource-optimization, workflow-enhancement                     |
| Path-Based Selective CI Building  | Thêm logic phát hiện file changes để chỉ build frontend/backend khi có thay đổi tương ứng                        | ci-cd, path-based-building, selective-ci, monorepo, optimization, dorny-paths-filter                              |
| PR #71 Review Comments Fix        | Fix toàn bộ 16 review comments từ PR #71 bao gồm security, documentation và CI improvements                      | pr-review, security-fix, documentation-improvement, ci-cd-optimization, markdown-formatting                       |
| GitHub Workflow Permissions Error | Sửa lỗi "Resource not accessible by integration" bằng cách thêm permissions cho workflow                         | github-actions, permissions, ci-cd, workflow, integration-error                                                   |
| PR #71 Documentation Issues Fix   | Sửa 4 issue trong PR #71: ApiClient visibility, markdown code fence, PlantUML markers, Spring Boot compatibility | documentation, markdown, typescript, spring-boot, code-review, pr-71                                              |
| Workflow Refactoring và Fixes     | Refactor requirements-change-review workflow với composite action, sửa head limits, giảm code duplication        | workflow-refactoring, composite-action, code-duplication, ci-cd, github-actions, requirements-review, maintenance |
| Null Check Response Body          | Sửa lỗi NullPointerException khi sử dụng response.getBody() mà không kiểm tra null trong REST controller         | java, null-safety, error-handling, defensive-programming, rest-controller, spring-boot                            |
