# Tóm tắt Guidelines

## Danh sách các hướng dẫn

| Tên hướng dẫn                             | Mô tả tóm tắt                                                                                                         | Tag                                                                                                                                                                                                    |
| ----------------------------------------- | --------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Vietnamese Comments Guideline             | Hướng dẫn viết comment tiếng Việt cho Frontend code, bao gồm quy tắc JSDoc và best practices                          | frontend, comment, vietnamese, jsdoc                                                                                                                                                                   |
| GitHub Issue Template Requirements Review | Template và workflow tự động tạo issue đánh giá tác động khi có thay đổi yêu cầu nghiệp vụ với AI integration         | github, automation, requirements, review, issue-template, ai, copilot                                                                                                                                  |
| GitHub CI/CD và Notification System       | Hướng dẫn cấu hình CI build automation và Lark notification cho requirements changes                                  | ci-cd, github-actions, build-automation, lark-notification, requirements-management                                                                                                                    |
| CI/CD Dependencies Fallback Strategy      | Hướng dẫn smart workflow optimization: fallback dependencies, smart CI logic, draft PR handling, duplicate prevention | ci-cd, dependencies, fallback, pnpm, frontend, build-optimization, draft-pr, pull-request, workflow-optimization, duplicate-prevention, resource-management, smart-logic, label-control, target-branch |