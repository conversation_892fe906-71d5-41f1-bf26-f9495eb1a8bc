Dưới đây là template của một tài liệu detailed design.

# Task 1.1: Setup Development Environment - Tài liệu Thiết kế Chi tiết

## 📋 Thông tin Task

| Thuộc tính       | Gi<PERSON> trị                                      |
| ---------------- | -------------------------------------------- |
| **Task ID**      | 1.1                                          |
| **Task Name**    | Setup Development Environment                |
| **User Story**   | US-007 (Authentication & Account Management) |
| **Sprint**       | Sprint 1 (Weeks 1-2)                         |
| **Story Points** | 3                                            |
| **PIC**          | DevOps Lead                                  |
| **Status**       | 🔴 Not Started                                |
| **Dependencies** | None (First task)                            |

## 🎯 Mục tiêu Task

Thiết lập môi trường phát triển hoàn chỉnh cho dự án PlantUML Diagram Tool, đảm bảo tất cả thành viên team có thể bắt đầu phát triển ngay lập tức với cấu hình thống nhất.

## 🏗️ Kiến trúc Hệ thống

### Tech Stack Overview
```mermaid
graph TB
    subgraph "Development Environment"
        A[.NET 9 SDK] --> B[Node.js 20+ LTS]
        B --> C[PostgreSQL on Supabase]
        C --> D[Docker Desktop]
        D --> E[PlantUML Server]
    end
    
    subgraph "Development Tools"
        F[Visual Studio 2022/VS Code] --> G[Git & GitHub]
        G --> H[Postman/Thunder Client]
        H --> I[pgAdmin/DBeaver]
    end
    
    A --> F
```text

## 📦 Yêu cầu Phần mềm

### Backend Development (.NET 9)
- **.NET 9 SDK** (Latest stable version)
- **Visual Studio 2022** (v17.8+) hoặc **VS Code** với C# extension
- **Entity Framework Core Tools** (dotnet-ef)
- **Docker Desktop** (cho PlantUML server)

### Frontend Development (React 19)
- **Node.js** v20+ LTS
- **npm** v10+ hoặc **yarn** v4+
- **VS Code** với React/TypeScript extensions

### Database (Supabase PostgreSQL)
- **Supabase CLI** (cho local development)
- **pgAdmin** hoặc **DBeaver** (database management)
- **PostgreSQL Client Tools**

### DevOps & Tools
- **Git** v2.40+
- **GitHub CLI** (gh)
- **Docker Desktop** v4.20+
- **Postman** hoặc **Thunder Client** (API testing)

## 🗄️ Cấu hình Database - Supabase

### Project Setup
```yaml
Project Name: diagram-editr
Region: ap-southeast-1 (Singapore)
Database: PostgreSQL 15
Authentication: Enabled
Storage: Enabled
Edge Functions: Enabled
```text

### Connection Configuration
```env
# Supabase Configuration
SUPABASE_URL=https://bnvlgoqmcjomglbdywis.supabase.co
SUPABASE_ANON_KEY=[anon_key]
SUPABASE_SERVICE_ROLE_KEY=[service_role_key]
DATABASE_URL=postgresql://postgres:[password]@db.bnvlgoqmcjomglbdywis.supabase.co:5432/postgres
```text

### Database Schema (Initial)
```sql
-- Users table (managed by Supabase Auth)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Basic RLS policy
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);
```text

## 🐳 Docker Configuration

### PlantUML Server Setup
```dockerfile
# docker-compose.yml
version: '3.8'
services:
  plantuml:
    image: plantuml/plantuml-server:jetty
    container_name: plantuml-server
    ports:
      - "8080:8080"
    environment:
      - PLANTUML_LIMIT_SIZE=4096
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/plantuml/"]
      interval: 30s
      timeout: 10s
      retries: 3
```text

### Development Services
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  plantuml:
    extends:
      file: docker-compose.yml
      service: plantuml
  
  # Optional: Local PostgreSQL for offline development
  postgres-local:
    image: postgres:15
    container_name: postgres-local
    environment:
      POSTGRES_DB: plantuml_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    profiles: ["local-db"]

volumes:
  postgres_data:
```text

## 🔧 Project Structure

### Backend Structure (.NET 9)
```text
PlantUMLTool/
├── src/
│   ├── PlantUMLTool.API/           # Web API layer
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   ├── Configuration/
│   │   ├── appsettings.json
│   │   ├── appsettings.Development.json
│   │   └── Program.cs
│   ├── PlantUMLTool.Application/   # Application layer
│   │   ├── Commands/
│   │   ├── Queries/
│   │   ├── Handlers/
│   │   ├── DTOs/
│   │   ├── Interfaces/
│   │   └── Services/
│   ├── PlantUMLTool.Domain/        # Domain layer
│   │   ├── Entities/
│   │   ├── ValueObjects/
│   │   ├── Interfaces/
│   │   └── Events/
│   └── PlantUMLTool.Infrastructure/ # Infrastructure layer
│       ├── Data/
│       │   ├── Configurations/
│       │   ├── Repositories/
│       │   └── PlantUMLContext.cs
│       ├── Services/
│       └── Extensions/
├── tests/
│   ├── PlantUMLTool.UnitTests/
│   ├── PlantUMLTool.IntegrationTests/
│   └── PlantUMLTool.E2ETests/
├── docs/
├── scripts/
├── .gitignore
├── README.md
├── docker-compose.yml
└── PlantUMLTool.sln
```text

### Frontend Structure (React 19)
```text
plantuml-frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Base UI components
│   │   ├── editor/         # Code editor related
│   │   ├── preview/        # Diagram preview
│   │   └── templates/      # Template components
│   ├── pages/              # Page components
│   │   ├── DiagramEditor/
│   │   ├── DiagramList/
│   │   ├── TemplateLibrary/
│   │   └── Auth/
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API services
│   ├── store/              # State management
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions
│   ├── styles/             # Global styles
│   ├── App.tsx
│   ├── main.tsx
│   └── vite-env.d.ts
├── tests/
├── .env.example
├── .env.local
├── .gitignore
├── package.json
├── tsconfig.json
├── vite.config.ts
└── README.md
```text

## ⚙️ Environment Configuration

### Backend (.NET 9)
```json
// appsettings.Development.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=db.bnvlgoqmcjomglbdywis.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=[password]"
  },
  "Supabase": {
    "Url": "https://bnvlgoqmcjomglbdywis.supabase.co",
    "Key": "[anon_key]",
    "ServiceKey": "[service_role_key]"
  },
  "PlantUML": {
    "ServerUrl": "http://localhost:8080/plantuml"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```text

### Frontend (React 19)
```env
# .env.local
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SUPABASE_URL=https://bnvlgoqmcjomglbdywis.supabase.co
VITE_SUPABASE_ANON_KEY=[anon_key]
VITE_PLANTUML_SERVER_URL=http://localhost:8080/plantuml
```text

## 🚀 Setup Instructions

### 1. Prerequisites Installation
```bash
# Install .NET 9 SDK
winget install Microsoft.DotNet.SDK.9

# Install Node.js 20 LTS
winget install OpenJS.NodeJS.LTS

# Install Docker Desktop
winget install Docker.DockerDesktop

# Install Git
winget install Git.Git

# Install GitHub CLI
winget install GitHub.cli
```text

### 2. Project Initialization
```bash
# Clone repository
git clone https://github.com/namnhcntt/trainning-ai.git
cd trainning-ai

# Create project directories
mkdir -p PlantUMLTool/src PlantUMLTool/tests
mkdir -p plantuml-frontend/src

# Initialize .NET solution
cd PlantUMLTool
dotnet new sln -n PlantUMLTool

# Initialize React project
cd ../plantuml-frontend
npm create vite@latest . -- --template react-ts
```text

### 3. Supabase Setup
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Link to existing project
supabase link --project-ref bnvlgoqmcjomglbdywis

# Pull remote schema
supabase db pull
```text

### 4. Docker Services
```bash
# Start PlantUML server
docker-compose up -d plantuml

# Verify PlantUML is running
curl http://localhost:8080/plantuml/
```text

## ✅ Verification Checklist

### Development Environment
- [ ] .NET 9 SDK installed và hoạt động
- [ ] Node.js 20+ LTS installed và npm/yarn available
- [ ] Docker Desktop running
- [ ] Git configured với GitHub credentials
- [ ] VS Code/Visual Studio với extensions cần thiết

### Database Connection
- [ ] Supabase project "diagram-editr" accessible
- [ ] Database connection string working
- [ ] Basic tables created
- [ ] RLS policies configured

### Services
- [ ] PlantUML server running on port 8080
- [ ] PlantUML health check passing
- [ ] Basic diagram rendering test successful

### Project Structure
- [ ] Backend solution structure created
- [ ] Frontend project initialized với React 19
- [ ] Environment files configured
- [ ] Git repository setup với proper .gitignore

## 🔒 Security Considerations

### Environment Variables
- Không commit sensitive keys vào Git
- Sử dụng .env.example files cho template
- Rotate keys định kỳ
- Sử dụng different keys cho mỗi environment

### Database Security
- Enable RLS trên tất cả tables
- Configure proper authentication policies
- Limit database access permissions
- Regular security audits

## 📊 Success Metrics

### Performance Targets
- PlantUML server response time < 2s
- Database connection time < 500ms
- Project build time < 30s (backend), < 60s (frontend)

### Quality Metrics
- Zero critical security vulnerabilities
- All services health checks passing
- 100% team members có working environment

## 🔄 Next Steps

Sau khi hoàn thành Task 1.1, team sẽ có:
1. Môi trường development hoàn chỉnh
2. Database schema cơ bản
3. PlantUML server integration
4. Project structure chuẩn

**Dependencies cho tasks tiếp theo:**
- Task 1.2: Initialize Backend Project Structure
- Task 1.3: Initialize Frontend Project Structure  
- Task 1.4: Setup Database Schema

---

*Tài liệu này sẽ được cập nhật khi có thay đổi trong requirements hoặc technical specifications.*