# Lỗi GitHub Workflow: "Resource not accessible by integration"

## Mô tả lỗi

Workflow CI bị lỗi với thông báo "Resource not accessible by integration" khi job **Detect File Changes** cố gắng truy cập thông tin về PR và files thay đổi.

### Lỗi chi tiết
- **Thông báo lỗi:** Resource not accessible by integration
- **Job bị lỗi:** Detect File Changes
- **Action gây lỗi:** dorny/paths-filter
- **Nguyên nhân:** Thiếu permissions để truy cập pull-requests và contents

## Phân tích nguyên nhân

Lỗi này xảy ra khi GitHub Action cố gắng truy cập các resource (như liệt kê PR files hoặc labels) mà không có đủ quyền. Cụ thể:

1. **Default token permissions:** GitHub token mặc định có quyền hạn chế
2. **Fork PRs:** <PERSON>hi làm việc với fork, quyền truy cập bị giới hạn thêm
3. **Missing explicit permissions:** Workflow không khai báo rõ ràng permissions cần thiết

## Giải pháp

### 1. Thêm Workflow Permissions

Thêm permissions block vào đầu file `.github/workflows/ci-build.yml`:

```yaml
name: CI Build

permissions:
  contents: read
  pull-requests: read

on:
  push:
    branches: [ main, develop ]
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review, labeled, unlabeled ]
```

### 2. Giải thích permissions

- **contents: read** - Cho phép đọc repository contents và files
- **pull-requests: read** - Cho phép đọc thông tin PR, labels, và file changes

### 3. Vị trí thêm permissions

Permissions block phải được đặt:
- Sau `name:` 
- Trước `on:`
- Ở top-level của workflow (không nested trong job)

## Kết quả

Sau khi thêm permissions:

1. ✅ Job "Detect File Changes" có thể truy cập PR information
2. ✅ Action dorny/paths-filter hoạt động bình thường
3. ✅ Workflow có thể detect file changes để selective building
4. ✅ CI pipeline chạy thành công cho cả push và pull request events

## Files thay đổi

- `.github/workflows/ci-build.yml` - Thêm permissions block

## Tags

`github-actions`, `permissions`, `ci-cd`, `workflow`, `integration-error`
