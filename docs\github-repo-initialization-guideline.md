# Hướng dẫn & Quy định khi tạo mới GitHub Repository

## 1. Quy trình Gitflow & Quy định quản lý nhánh

### 1.1. <PERSON><PERSON><PERSON> lo<PERSON>i nh<PERSON>h chính

- `main`: Chứa code production.
- `develop`: Tổng hợp code phát triển.
- `feature/<ticket_id>-<mô_tả_ngắn>`: <PERSON><PERSON><PERSON> triển tính năng mới.
- `bugfix/<ticket_id>-<mô_tả_ngắn>`: Sửa lỗi.
- `release/<ticket_id>-<mô_tả_ngắn>`: Chuẩn bị release.
- `hotfix/<ticket_id>-<mô_tả_ngắn>`: Sửa lỗi production.
- `private/<tên_tài_khoản>/*`: <PERSON>h<PERSON>h cá nhân dành cho từng user, dùng để phát triển hoặc thử nghiệm riêng.

### 1.2. <PERSON> quy định quyền push code và quy trình CI/CD

| Nhánh       | Merge tới         | Commit trực tiế<PERSON> | <PERSON>rge qua <PERSON> | Số người reviewer | CI  | CD         |
| ----------- | ----------------- | ---------------- | ------------ | ----------------- | --- | ---------- |
| `main`      |                   | No               | Yes          | ≥ 1               | Yes | Production |
| `develop`   |                   | No               | Yes          | ≥ 1               | Yes | Staging    |
| `release/*` | `main`, `develop` | Leader only      | Yes          | ≥ 1               | Yes | Optional   |
| `feature/*` | `develop`         | No               | Yes          | ≥ 1               | Yes | No         |
| `bugfix/*`  | `develop`         | No               | Yes          | ≥ 1               | Yes | No         |
| `hotfix/*`  | `main`, `develop` | Leader only      | Yes          | ≥ 1               | Yes | Optional   |
| `private/*` |                   | Yes              | No           | 0                 | No  | No         |

### 1.3 Sơ đồ Gitflow (GitGraph)

```mermaid
gitGraph
  commit id: "Initial commit" tag: "main"

  branch develop
  commit id: "Start development"


  branch feature/a
  commit id: "Start feature"

  branch private/account/a
  commit
  commit

  checkout feature/a
  merge private/account/a

  checkout develop
  merge feature/a


  branch bugfix/a
  commit id: "Start bugfix"

  branch private/account/b
  commit
  commit

  checkout bugfix/a
  merge private/account/b

  checkout develop
  merge bugfix/a


  branch release/a
  commit id: "Start release"

  branch hotfix/a
  commit id: "Start hotfix a"

  branch private/account/c
  commit
  commit

  checkout release/a
  commit

  checkout hotfix/a
  merge private/account/c
  commit

  checkout release/a
  merge hotfix/a

  checkout develop
  merge release/a

  checkout main
  merge release/a


  branch hotfix/b
  commit id: "Start hotfix b"
  commit

  branch private/account/d
  commit
  commit

  checkout hotfix/b
  merge private/account/d

  checkout develop
  merge hotfix/b

  checkout main
  merge hotfix/b
```text

---

## 2. Quy định về Release (Tag)

- Đánh tag theo chuẩn [SemVer (Vietnamese)](https://semver.org/lang/vi/) | [SemVer (English)](https://semver.org/): `vX.Y.Z` (ví dụ: v1.0.0), trong đó:
  - `MAJOR`: Thay đổi lớn, không tương thích ngược.
  - `MINOR`: Thêm tính năng mới, tương thích ngược.
  - `PATCH`: Sửa lỗi, tương thích ngược.
- Release note phải rõ ràng, liệt kê các thay đổi chính.
- Tag chỉ được tạo trên nhánh `main`.

---

## 3. GitHub Template

- **Issue Template** thiết lập trong file
  - `.github/ISSUE_TEMPLATE/bug_report.yaml`
  - `.github/ISSUE_TEMPLATE/feature_request.yaml`
- **Task Template cho AI agent** thiết lập trong file
  - `.github/ISSUE_TEMPLATE/task.yaml`.

---

## 4. Quy định về Pull Request (PR)

- Thiết lập trong file `.github/PULL_REQUEST_TEMPLATE.md`.
- **Tiêu đề:**
  - [FEATURE] <Ticket_ID> - Tiêu đề tính năng
  - [FIX] <Ticket_ID> - Tiêu đề lỗi
  - [HOTFIX] <Ticket_ID> - Tiêu đề hotfix
  - [TEST] <Ticket_ID> - Mô tả viết Unit Test
  - [REFACTOR] <Ticket_ID> - Mô tả việc refactor và tái cấu trúc
  - [PERF] <Ticket_ID> - Mô tả xử lý tăng hiệu năng
  - [CHORE] Mô tả update dependencies
  - [CI] Mô tả update cấu hình CI/CD
  - [RELEASE] Chuẩn bị release vX.Y.Z
  - [DOCS] Mô tả tài liệu update
- **Assign review:**
  - PR phải có ít nhất 1 reviewer.
  - Reviewer là leader hoặc người cùng team.
- **CI pipeline trên PR:**
  - Chạy test, lint, build.
  - Không cho phép merge nếu fail.
- **Checklist PR:**
  - Đã test local
  - Đã cập nhật tài liệu (nếu cần)
  - Đã cập nhật migration/script (nếu có)

---

## 5. Environment variable / Secret variable

- Khai báo trong GitHub Settings > Secrets and variables > Actions.
- Đặt tên rõ ràng, viết hoa, phân biệt môi trường: `PROD_DB_URL`, `STG_DB_URL`, `DEV_API_KEY`...
- Không commit file chứa secret lên repository.

---

## 6. Checklist khi tạo mới repository

### 6.1. Thiết lập cấu trúc nhánh

- [ ] Tạo nhánh `main` (nhánh production)
- [ ] Tạo nhánh `develop` (nhánh phát triển)
- [ ] Thiết lập branch protection rule cho `main`:
  - [ ] Yêu cầu review pull request (≥ 1 reviewer)
  - [ ] Yêu cầu status checks phải pass trước khi merge
  - [ ] Hạn chế push trực tiếp
- [ ] Thiết lập branch protection rule cho `develop`:
  - [ ] Yêu cầu review pull request (≥ 1 reviewer)
  - [ ] Yêu cầu status checks phải pass trước khi merge

### 6.2. Thiết lập Issue Templates

- [ ] Tạo `.github/ISSUE_TEMPLATE/bug_report.yaml`
- [ ] Tạo `.github/ISSUE_TEMPLATE/feature_request.yaml`
- [ ] Tạo `.github/ISSUE_TEMPLATE/task.yaml` (cho AI agent)

### 6.3. Thiết lập Pull Request

- [ ] Tạo `.github/PULL_REQUEST_TEMPLATE.md`
- [ ] Cấu hình PR title conventions:
  - [FEATURE], [FIX], [HOTFIX], [TEST], [REFACTOR], [PERF], [CHORE], [CI], [RELEASE], [DOCS]
- [ ] Thiết lập reviewer requirements (≥ 1 reviewer)

### 6.4. Thiết lập CI/CD

- [ ] Cấu hình GitHub Actions cho nhánh `main` và `develop`
- [ ] Thiết lập CI pipeline cho PR (test, lint, build)
- [ ] Cấu hình CD cho production (từ `main`)
- [ ] Cấu hình CD cho staging/development (từ `develop`)

### 6.5. Thiết lập Release & Tagging

- [ ] Thiết lập release notes template
- [ ] Chỉ cho phép tạo tag trên nhánh `main`

### 6.6. Tài liệu & Hướng dẫn

- [ ] Tạo `README.md` với hướng dẫn sử dụng repository
- [ ] Tạo `CONTRIBUTING.md` (nếu cần)
- [ ] Tạo `LICENSE` file
- [ ] Tạo `RELEASE_NOTES.md`

### 6.7. Bảo mật & Environment

- [ ] Khai báo environment variables trong GitHub Settings > Secrets and variables > Actions
- [ ] Đặt tên biến môi trường theo chuẩn: `PROD_DB_URL`, `DEV_API_KEY`
- [ ] Thêm `.gitignore` để tránh commit secrets
- [ ] Bật GitHub secret-scanning & push protection
- [ ] Kích hoạt Dependabot alerts & version-update PRs
- [ ] Bật CodeQL để quét lỗ hổng bảo mật
- [ ] Thiết lập branch-protection rule yêu cầu status check "Security Scan" pass

### 6.8. Cấu hình bổ sung

- [ ] Thiết lập labels cho issues và PRs
- [ ] Thiết lập labels cho AI agent
- [ ] Cấu hình notifications cho team
- [ ] Thiết lập repository settings (visibility, features, etc.)
