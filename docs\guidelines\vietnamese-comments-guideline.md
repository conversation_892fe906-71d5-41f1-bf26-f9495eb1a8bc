# Hướng dẫn viết Comment tiếng Việt cho Frontend

## 1. <PERSON><PERSON><PERSON><PERSON> tắc chung

### 1.1. <PERSON>ôn ngữ
- Sử dụng **tiếng Việt** cho tất cả comment
- <PERSON><PERSON><PERSON> nguyên thuật ngữ kỹ thuật **tiếng Anh** (component, hook, interface, props, state, etc.)
- V<PERSON><PERSON><PERSON> rõ ràng, ngắn gọn và đi thẳng vào vấn đề

### 1.2. Format
- Sử dụng **JSDoc** format với `/** */`
- Mỗi comment bắt đầu bằng mô tả ngắn gọn
- Sử dụng `@param`, `@returns`, `@throws` khi cần thiết

## 2. Quy tắc cho từng loại

### 2.1. Components

```typescript
/**
 * Component Button có thể tùy chỉnh với nhiều variant và size khác nhau
 * Hỗ trợ các accessibility features và có thể render thành element khác thông qua asChild
 * 
 * @param className - Class CSS bổ sung
 * @param variant - Kiểu button (default, destructive, outline, secondary, ghost, link)
 * @param size - Kích thước button (default, sm, lg, icon)
 * @param asChild - Nếu true, sẽ render thành child element thay vì button
 * @returns JSX.Element
 */
function Button({ className, variant, size, asChild = false, ...props }) {
  // ...
}
```text

### 2.2. Hooks

```typescript
/**
 * Custom hook để sử dụng authentication context
 * Cung cấp quyền truy cập vào auth state và functions
 * 
 * @returns AuthContextType - Object chứa auth state và methods
 * @throws Error nếu được sử dụng bên ngoài AuthProvider
 */
export const useAuth = (): AuthContextType => {
  // ...
}
```text

### 2.3. Interfaces/Types

```typescript
/**
 * Interface định nghĩa model cho User entity
 */
export interface UserModel {
  /** ID duy nhất của user */
  id: number;
  /** Tên đăng nhập của user */
  username: string;
  /** Địa chỉ email của user */
  email: string;
  /** Tên của user (tùy chọn) */
  firstName?: string;
}
```text

### 2.4. Functions/Methods

```typescript
/**
 * Function xử lý đăng nhập user
 * @param userData - Thông tin user để set vào state
 */
const login = (userData: UserModel) => {
  // ...
}
```text

### 2.5. Constants/Variables

```typescript
/** Font Geist cho chữ sans-serif */
const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

/** State lưu trữ thông tin user hiện tại */
const [user, setUser] = useState<UserModel | null>(null);
```text

### 2.6. File exports

```typescript
/**
 * Export tất cả các UI components
 * File này tập hợp và export các component từ package ui
 */
export * from "./button";
```text

## 3. Ví dụ thực tế

### 3.1. Component Page

```typescript
/**
 * Component trang chủ admin
 * Hiển thị giao diện đơn giản với greeting và button demo
 */
export default function Page() {
  return (
    <div className="flex items-center justify-center min-h-svh">
      <div className="flex flex-col items-center justify-center gap-4">
        <h1 className="text-2xl font-bold">Hello World</h1>
        <Button size="sm">Button</Button>
      </div>
    </div>
  );
}
```text

### 3.2. Provider Component

```typescript
/**
 * AuthProvider component cung cấp authentication context cho toàn bộ ứng dụng
 * Quản lý state đăng nhập/đăng xuất của user
 * 
 * @param children - React components con sẽ có quyền truy cập auth context
 */
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // ...
}
```text

### 3.3. Utility Function

```typescript
/**
 * Utility function để kết hợp các class names
 * Sử dụng clsx để xử lý conditional classes và twMerge để merge Tailwind classes
 * 
 * @param inputs - Mảng các class values có thể là string, object, array, undefined
 * @returns Chuỗi class names đã được merge và optimized
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```text

## 4. Lưu ý quan trọng

### 4.1. Không comment quá chi tiết
- Không cần comment cho code đơn giản, self-explanatory
- Tập trung vào **tại sao** thay vì **làm gì**
- Comment cho business logic phức tạp

### 4.2. Cập nhật comment
- Luôn cập nhật comment khi thay đổi code
- Xóa comment obsolete
- Đảm bảo comment phản ánh đúng behavior hiện tại

### 4.3. Tuân thủ Google Style Guide
- Theo chuẩn JSDoc format
- Sử dụng `@param`, `@returns`, `@throws` đúng syntax
- Giữ line length phù hợp (thường < 100 characters)

## 5. Tools hỗ trợ

### 5.1. VSCode Extensions
- **Auto Comment Blocks** - Tự động tạo JSDoc template
- **Better Comments** - Highlight different types of comments
- **Document This** - Generate JSDoc comments

### 5.2. ESLint Rules
- `valid-jsdoc` - Validate JSDoc syntax
- `require-jsdoc` - Require JSDoc for functions/classes
- `jsdoc/require-description` - Require description in JSDoc