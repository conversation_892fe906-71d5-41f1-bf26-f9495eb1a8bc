# Sửa các Issue trong Pull Request #71

## Tóm tắt
Đã sửa thành công 4 issue trong Pull Request #71 của repository ospgroupvn/cms liên quan đến documentation và code visibility.

## Chi tiết các Issue đã sửa

### Issue 1: ApiClient visibility trong component-architecture.md
**File:** `docs/architecture-design/component-architecture.md` (dòng 194)

**Vấn đề:** 
- Thu<PERSON><PERSON> tính `instance` của class `ApiClient` được khai báo là `private`
- Subclass `UserApiClient` không thể truy cập `this.instance` gây lỗi compilation

**Giải pháp đã áp dụng:**
```typescript
// Trước
class ApiClient {
  private instance: AxiosInstance;

// Sau  
class ApiClient {
  protected instance: AxiosInstance;
```

**Kết quả:** Subclass `UserApiClient` giờ có thể truy cập `this.instance` mà không gặp lỗi compilation.

### Issue 2: Markdown code fence không đóng đúng cách trong README.md
**File:** `docs/architecture-design/README.md` (dòng 96-97)

**Vấn đề:**
- Code fence markdown mở tại dòng 48 không được đóng đúng cách
- Text fence ````text` tại dòng 97 gây lỗi rendering

**Giải pháp đã áp dụng:**
- Đóng code block markdown bằng cách thêm ````` tại dòng 97
- Loại bỏ ````text` không cần thiết

**Kết quả:** Markdown rendering hoạt động chính xác, không còn lỗi format.

### Issue 3: PlantUML diagram thiếu code block markers trong README.md
**File:** `docs/architecture-design/README.md` (dòng 404-474)

**Vấn đề:**
- PlantUML code bắt đầu với `@startuml` thiếu fenced code block markers
- Không có syntax highlighting và khó copy code

**Giải pháp đã áp dụng:**
```markdown
// Trước
@startuml C4\_Container\_View
...
@enduml

// Sau
```plantuml
@startuml C4\_Container\_View
...
@enduml
```
```

**Kết quả:** PlantUML code có syntax highlighting và dễ dàng copy.

### Issue 4: Làm rõ thông tin về Spring Boot compatibility trong system-architecture.md
**File:** `docs/architecture-design/system-architecture.md` (dòng 22)

**Vấn đề:**
- Ghi chú về Spring Boot 1.2.7 không tương thích với Java 21 cần được làm rõ
- Thiếu hướng dẫn cụ thể để giải quyết vấn đề

**Giải pháp đã áp dụng:**
```markdown
// Trước
- **Backend:** Spring Boot 1.2.7 (⚠️ compatibility issue với Java 21)

// Sau
- **Backend:** Spring Boot 1.2.7 (⚠️ **Khuyến nghị:** Upgrade lên Spring Boot 3.x để tương thích với Java 21 và nhận long-term support + security patches, hoặc downgrade JDK xuống version 17)
```

**Kết quả:** Documentation cung cấp hướng dẫn rõ ràng và có thể thực hiện được.

## Xác minh
- ✅ Tất cả các thay đổi đã được kiểm tra và xác minh
- ✅ Markdown rendering hoạt động chính xác
- ✅ Không có ảnh hưởng đến nội dung hoặc cấu trúc khác của tài liệu
- ✅ Code visibility issue đã được giải quyết

## Tags
`documentation`, `markdown`, `typescript`, `spring-boot`, `code-review`, `pr-71`
