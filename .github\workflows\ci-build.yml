name: CI Build

on:
  push:
    branches: [ main, develop ]
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review, labeled, unlabeled ]

concurrency:
  group: ci-build-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Kiểm tra điều kiện để bỏ qua draft PR
  check-conditions:
    name: Check Build Conditions
    runs-on: ubuntu-latest
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: Check if should build
        id: check
        run: |
          set -euo pipefail
          echo "🔍 Kiểm tra điều kiện build..."
          echo "Event: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Base Ref: ${{ github.base_ref }}"
          
          # Nếu là push event (chỉ cho main/develop), luôn build
          if [[ "${{ github.event_name }}" == "push" ]]; then
            echo "📤 Push event to protected branch detected - sẽ chạy build"
            echo "should-build=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # Nếu là PR event, kiểm tra điều kiện phức tạp
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "🔀 Pull Request event detected"
            echo "PR Draft Status: ${{ github.event.pull_request.draft }}"
            echo "Target Branch: ${{ github.event.pull_request.base.ref }}"
            
            # Kiểm tra draft status trước
            if [[ "${{ github.event.pull_request.draft }}" == "true" ]]; then
              echo "📝 Draft PR detected - bỏ qua build"
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi
            
            # Kiểm tra target branch
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            echo "🎯 Analyzing target branch: $TARGET_BRANCH"
            
            if [[ "$TARGET_BRANCH" == "main" || "$TARGET_BRANCH" == "develop" ]]; then
              echo "✅ PR targeting protected branch ($TARGET_BRANCH) - bắt buộc chạy build"
              echo "should-build=true" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "🔍 PR targeting other branch ($TARGET_BRANCH) - kiểm tra label 'ci-build'"
              
              # Kiểm tra label ci-build
              LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
              echo "Labels: $LABELS"
              
              if echo "$LABELS" | grep -q "ci-build"; then
                echo "🏷️ Label 'ci-build' được tìm thấy - sẽ chạy build"
                echo "should-build=true" >> $GITHUB_OUTPUT
              else
                echo "⏭️ Không có label 'ci-build' - bỏ qua build"
                echo "should-build=false" >> $GITHUB_OUTPUT
              fi
              exit 0
            fi
          fi
          
          # Default: build
          echo "⚠️ Unexpected event type - default to build"
          echo "should-build=true" >> $GITHUB_OUTPUT

  skip-build-notification:
    name: Skip Build Notification
    runs-on: ubuntu-latest
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'false'
    
    steps:
      - name: Notify Skip Build
        run: |
          echo "⏭️ CI Build được bỏ qua"
          echo ""
          echo "📋 Điều kiện build:"
          echo "✅ PR vào main/develop: Luôn chạy build"
          echo "🏷️ PR vào branch khác: Cần label 'ci-build'"
          echo "📝 Draft PR: Luôn bỏ qua"
          echo ""
          echo "💡 Tips:"
          echo "- Thêm label 'ci-build' vào PR để kích hoạt build"
          echo "- Chuyển PR sang 'Ready for Review' nếu đang ở draft"
          echo "✅ Workflow hoàn thành thành công (skipped)"

  detect-changes:
    name: Detect File Changes
    runs-on: ubuntu-latest
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      backend-changed: ${{ steps.changes.outputs.backend }}
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      docs-changed: ${{ steps.changes.outputs.docs }}
      ci-changed: ${{ steps.changes.outputs.ci }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: Detect file changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            backend:
              - 'src/backend/**'
              - 'docs/backend/**'
            frontend:
              - 'src/frontend/**'
              - 'docs/frontend/**'
            docs:
              - 'docs/requirements/**'
              - 'docs/architecture/**'
              - 'docs/guidelines/**'
              - 'docs/issues/**'
            ci:
              - '.github/workflows/**'
      
      - name: Log detected changes
        run: |
          echo "🔍 File Changes Detection Results:"
          echo "Backend changes: ${{ steps.changes.outputs.backend }}"
          echo "Frontend changes: ${{ steps.changes.outputs.frontend }}"
          echo "Docs changes: ${{ steps.changes.outputs.docs }}"
          echo "CI changes: ${{ steps.changes.outputs.ci }}"

  build-backend:
    name: Build Backend (Java Spring Boot)
    runs-on: ubuntu-latest
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' && 
      (needs.detect-changes.outputs.backend-changed == 'true' || 
       needs.detect-changes.outputs.ci-changed == 'true' ||
       needs.detect-changes.outputs.docs-changed == 'true')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Log build reason
        run: |
          echo "🔨 Building Backend - Reasons:"
          echo "Backend changes: ${{ needs.detect-changes.outputs.backend-changed }}"
          echo "CI changes: ${{ needs.detect-changes.outputs.ci-changed }}"
          echo "Docs changes: ${{ needs.detect-changes.outputs.docs-changed }}"
        
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
          
      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
          
      - name: Build Backend
        run: |
          echo "🔨 Building Backend Spring Boot project..."
          cd src/backend
          mvn clean compile
          echo "✅ Backend build completed successfully!"
          
      - name: Run Backend Tests (if any)
        run: |
          echo "🧪 Running Backend tests..."
          cd src/backend
          mvn test
          echo "✅ Backend tests completed!"

  build-frontend:
    name: Build Frontend (NextJS TypeScript)
    runs-on: ubuntu-latest
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' && 
      (needs.detect-changes.outputs.frontend-changed == 'true' || 
       needs.detect-changes.outputs.ci-changed == 'true' ||
       needs.detect-changes.outputs.docs-changed == 'true')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Log build reason
        run: |
          echo "🔨 Building Frontend - Reasons:"
          echo "Frontend changes: ${{ needs.detect-changes.outputs.frontend-changed }}"
          echo "CI changes: ${{ needs.detect-changes.outputs.ci-changed }}"
          echo "Docs changes: ${{ needs.detect-changes.outputs.docs-changed }}"
        
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          
      - name: Cache pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: ${{ runner.os }}-pnpm-
          
      - name: Install Frontend Dependencies
        run: |
          echo "📦 Installing Frontend dependencies..."
          cd src/frontend
          
          # Kiểm tra và thử cài dependencies với pnpm-lock.yaml
          if [ -f "pnpm-lock.yaml" ]; then
            echo "🔍 Tìm thấy pnpm-lock.yaml, thử cài dependencies với --frozen-lockfile..."
            if pnpm install --frozen-lockfile; then
              echo "✅ Cài dependencies thành công với pnpm-lock.yaml!"
            else
              echo "⚠️ Không thể cài dependencies với pnpm-lock.yaml, chuyển sang fallback..."
              echo "🔄 Cài dependencies theo cách thông thường..."
              pnpm install
              echo "✅ Cài dependencies thành công với fallback!"
            fi
          else
            echo "⚠️ Không tìm thấy pnpm-lock.yaml, cài dependencies theo cách thông thường..."
            pnpm install
            echo "✅ Dependencies được cài đặt thành công!"
          fi
          
      - name: Build Frontend
        run: |
          echo "🔨 Building Frontend NextJS projects..."
          cd src/frontend
          pnpm build
          echo "✅ Frontend build completed successfully!"
          
      - name: Run Frontend Tests
        run: |
          echo "🧪 Running Frontend tests..."
          cd src/frontend
          pnpm test
          echo "✅ Frontend tests completed!"

  build-status:
    name: Build Status Summary
    runs-on: ubuntu-latest
    needs: [check-conditions, detect-changes, build-backend, build-frontend]
    if: always() && needs.check-conditions.outputs.should-build == 'true'
    
    steps:
      - name: Smart Build Summary
        run: |
          echo "📊 Path-Based Selective Build Results:"
          echo ""
          echo "🔍 File Changes Detection:"
          echo "  Backend: ${{ needs.detect-changes.outputs.backend-changed }}"
          echo "  Frontend: ${{ needs.detect-changes.outputs.frontend-changed }}"
          echo "  Docs: ${{ needs.detect-changes.outputs.docs-changed }}"
          echo "  CI: ${{ needs.detect-changes.outputs.ci-changed }}"
          echo ""
          
          # Backend status
          BACKEND_SHOULD_BUILD="false"
          if [[ "${{ needs.detect-changes.outputs.backend-changed }}" == "true" || "${{ needs.detect-changes.outputs.ci-changed }}" == "true" || "${{ needs.detect-changes.outputs.docs-changed }}" == "true" ]]; then
            BACKEND_SHOULD_BUILD="true"
            echo "🔨 Backend Build: ${{ needs.build-backend.result }}"
          else
            echo "⏭️ Backend Build: Skipped (no relevant changes)"
          fi
          
          # Frontend status
          FRONTEND_SHOULD_BUILD="false"  
          if [[ "${{ needs.detect-changes.outputs.frontend-changed }}" == "true" || "${{ needs.detect-changes.outputs.ci-changed }}" == "true" || "${{ needs.detect-changes.outputs.docs-changed }}" == "true" ]]; then
            FRONTEND_SHOULD_BUILD="true"
            echo "🔨 Frontend Build: ${{ needs.build-frontend.result }}"
          else
            echo "⏭️ Frontend Build: Skipped (no relevant changes)"
          fi
          
          echo ""
          
          # Check overall success
          OVERALL_SUCCESS="true"
          
          if [[ "$BACKEND_SHOULD_BUILD" == "true" && "${{ needs.build-backend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Backend build was required but failed!"
          fi
          
          if [[ "$FRONTEND_SHOULD_BUILD" == "true" && "${{ needs.build-frontend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Frontend build was required but failed!"
          fi
          
          if [[ "$OVERALL_SUCCESS" == "true" ]]; then
            echo "🎉 All required builds completed successfully!"
            echo "💡 Path-based selective building optimized CI time and resources!"
          else
            echo "💥 One or more required builds failed!"
            exit 1
          fi

  no-changes-notification:
    name: No Changes Notification
    runs-on: ubuntu-latest
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' && 
      needs.detect-changes.outputs.backend-changed == 'false' && 
      needs.detect-changes.outputs.frontend-changed == 'false' && 
      needs.detect-changes.outputs.ci-changed == 'false' &&
      needs.detect-changes.outputs.docs-changed == 'false'
    
    steps:
      - name: Notify No Relevant Changes
        run: |
          echo "🔍 Path-Based Analysis: No Relevant Changes Detected"
          echo ""
          echo "📁 Changed paths analysis showed no files in:"
          echo "  ❌ src/backend/**"
          echo "  ❌ src/frontend/**"  
          echo "  ❌ docs/requirements/**"
          echo "  ❌ docs/architecture/**"
          echo "  ❌ docs/guidelines/**"
          echo "  ❌ docs/issues/**"
          echo "  ❌ .github/workflows/**"
          echo ""
          echo "⏭️ Both Frontend and Backend builds skipped"
          echo "💡 This optimization saved significant CI time and resources!"
          echo "✅ Workflow completed successfully (selective skip)"
