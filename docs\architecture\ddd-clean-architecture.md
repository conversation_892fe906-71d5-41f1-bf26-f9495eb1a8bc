# Kiến trúc DDD/Clean Architecture - Backend

## Tổng quan

Backend đã được thiết kế lại theo nguyên tắc Domain-Driven Design (DDD) và Clean Architecture với cấu trúc 4 lớp chính:

## Cấu trúc thư mụ<PERSON>

```text
src/main/java/com/terragon/
├── domain/          # Domain Layer
│   ├── model/       # Entities và Value Objects
│   ├── repository/  # Repository interfaces
│   └── service/     # Domain Services
├── application/     # Application Layer
│   ├── dto/         # Data Transfer Objects
│   └── usecase/     # Use Cases (Application Services)
├── infrastructure/  # Infrastructure Layer
│   ├── database/    # Database implementations
│   ├── external/    # External API integrations
│   ├── security/    # Security configurations
│   └── storage/     # File storage services
└── presentation/    # Presentation Layer
    ├── controller/  # REST Controllers
    └── config/      # Configuration classes
```text

## Chi tiết từng lớp

### 1. Domain Layer (Lớp miền)
- **Entities**: Các đối tượng domain chính (User, BaseEntity)
- **Repository Interfaces**: <PERSON><PERSON><PERSON> nghĩa contract cho data access
- **Domain Services**: Logic nghiệp vụ phức tạp không thuộc về entity

### 2. Application Layer (Lớp ứng dụng) 
- **Use Cases**: Orchestrate business logic và coordinate các domain objects
- **DTOs**: Data transfer objects cho communication giữa các layer
- **Application Services**: Implement use cases

### 3. Infrastructure Layer (Lớp hạ tầng)
- **Database**: Implement repository interfaces
- **External Services**: Tích hợp với Keycloak, MinIO
- **Security**: Cấu hình bảo mật
- **Storage**: File storage với MinIO

### 4. Presentation Layer (Lớp trình bày)
- **Controllers**: REST API endpoints
- **Configuration**: Spring configuration classes

## Tech Stack mới

### Core Framework
- **Spring Boot**: 3.2.0 (tương thích Java 21)
- **Java**: 21
- **Maven**: Build tool

### Database
- **PostgreSQL**: Production database 
- **Spring Data JPA**: ORM framework
- **H2**: In-memory database cho testing

### Authentication & Authorization
- **Keycloak**: Identity và Access Management
- **Spring Security**: Security framework

### File Storage
- **MinIO**: Object storage service tương thích S3

### Others
- **Lombok**: Giảm boilerplate code
- **Flyway**: Database migration (optional)

## Cấu hình

### Database
```properties
# PostgreSQL (Production)
spring.datasource.url=********************************************
spring.datasource.username=terragon_user
spring.datasource.password=terragon_password

# H2 (Development - application-dev.properties)
spring.datasource.url=jdbc:h2:mem:testdb
```text

### Keycloak
```properties
keycloak.realm=terragon-realm
keycloak.auth-server-url=http://localhost:8080/auth
keycloak.resource=terragon-backend
```text

### MinIO
```properties
minio.endpoint=http://localhost:9000
minio.access-key=minioadmin
minio.secret-key=minioadmin
minio.bucket-name=terragon-bucket
```text

## API Endpoints

### User Management
- `POST /api/users` - Tạo user mới
- `GET /api/users/{id}` - Lấy user theo ID
- `GET /api/users/email/{email}` - Lấy user theo email
- `GET /api/users/keycloak/{keycloakId}` - Lấy user theo Keycloak ID
- `GET /api/users` - Lấy danh sách users (có phân trang)
- `DELETE /api/users/{id}` - Xóa user

### File Management
- `POST /api/files/upload` - Upload file
- `GET /api/files/download/{fileName}` - Download file
- `DELETE /api/files/{fileName}` - Xóa file

## Nguyên tắc thiết kế

### Dependency Rule
- Dependencies chỉ đi từ outer layer vào inner layer
- Domain layer không phụ thuộc vào bất kỳ layer nào khác
- Infrastructure layer implement interfaces từ Domain layer

### Clean Code
- Tuân thủ SOLID principles
- Single Responsibility Principle cho mỗi class
- Dependency Injection thông qua constructor
- Validation sử dụng Bean Validation

### Error Handling
- Domain exceptions cho business logic errors
- Global exception handler cho REST API
- Meaningful error messages trong tiếng Việt

## Migration từ cấu trúc cũ

### Những thay đổi chính:
1. **Spring Boot**: 1.2.7 → 3.2.0
2. **Database**: H2 → PostgreSQL
3. **Architecture**: Monolithic → DDD/Clean Architecture
4. **Security**: Basic → Keycloak integration
5. **File Storage**: Local → MinIO

### Backward compatibility:
- Các API endpoint cũ sẽ được maintain trong thời gian transition
- Database migration scripts được cung cấp
- Configuration profiles cho development và production