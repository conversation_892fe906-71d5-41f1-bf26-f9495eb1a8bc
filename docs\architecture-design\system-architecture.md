# System Architecture Design

## 1. Overview

### 1.1. <PERSON><PERSON><PERSON> đích hệ thống
Hệ thống C<PERSON> được thiết kế theo kiến trúc monorepo với frontend và backend tách bi<PERSON><PERSON>, hỗ trợ việc phát triển và maintain hiệu quả các ứng dụng web hiện đại.

### 1.2. Stakeholders
- **Development Team:** Frontend developers, Backend developers, DevOps engineers
- **Business Team:** Product Owners, Business Analysts
- **End Users:** Admin users, Web users

### 1.3. Success Criteria
- Hệ thống scalable và maintainable
- Performance cao với response time < 200ms
- Security tốt với authentication/authorization
- Developer experience tối ưu

## 2. Architecture Constraints

### 2.1. Technical Constraints
- **Backend:** Spring Boot 1.2.7 (⚠️ **Khuyến nghị:** Upgrade lên Spring Boot 3.x để tương thích với Java 21 và nhận long-term support + security patches, hoặc downgrade JDK xuống version 17)
- **Frontend:** NextJS 15.2.3 với React 19
- **Database:** H2 (development), PostgreSQL (production)
- **Package Manager:** PNPM với monorepo structure
- **Build System:** Maven (backend), Turbo (frontend)

### 2.2. Business Constraints
- Budget limited cho infrastructure
- Timeline tight cho first release
- Team size nhỏ (cần architecture đơn giản)

### 2.3. Time Constraints
- MVP trong 3 tháng
- Full production-ready trong 6 tháng

## 3. Architecture Decisions

### 3.1. Monorepo Structure
**Decision:** Sử dụng monorepo với Turbo để manage frontend packages

**Rationale:**
- Code sharing dễ dàng giữa admin và web apps
- Consistent tooling và dependencies
- Simplified CI/CD pipeline
- Better coordination between teams

**Alternatives Considered:**
- Multi-repo: Rejected vì complexity trong dependency management
- Lerna: Rejected vì Turbo có better performance

### 3.2. Technology Stack

#### Backend Technology Stack
**Decision:** Java Spring Boot với H2/PostgreSQL

**Rationale:**
- Team có experience với Java Spring Boot
- Ecosystem mature với nhiều libraries
- Good performance cho enterprise applications

**⚠️ Critical Issue:** Spring Boot 1.2.7 không compatible với Java 21
**Resolution Required:** Upgrade Spring Boot to 3.2+ hoặc downgrade Java to 17

#### Frontend Technology Stack
**Decision:** NextJS 15 với React 19, TypeScript, TailwindCSS

**Rationale:**
- Server-side rendering cho SEO
- File-based routing system
- Built-in optimization features
- Strong TypeScript support
- Modern React features (React 19)

### 3.3. State Management
**Decision:** Zustand cho client-side state management

**Rationale:**
- Lightweight alternative to Redux
- Better TypeScript support
- Simple API và learning curve thấp
- Good performance với minimal boilerplate

## 4. System Context

### 4.1. External Systems
- **Authentication Provider:** Built-in authentication system
- **Database:** PostgreSQL (production), H2 (development)
- **File Storage:** Local filesystem (có thể mở rộng thành cloud storage)
- **Email Service:** SMTP server cho notifications

### 4.2. Users và Actors
- **Admin Users:** Manage content, users, system configuration
- **Web Users:** Browse và interact với public content
- **System Administrators:** Deploy, monitor, maintain system

### 4.3. System Boundaries
```mermaid
graph TB
    A[Web Users] --> B[Web App NextJS]
    C[Admin Users] --> D[Admin App NextJS]
    B --> E[Backend API Spring Boot]
    D --> E
    E --> F[PostgreSQL Database]
    E --> G[File System]
    H[System Admin] --> I[CI/CD Pipeline]
    I --> E
    I --> B
    I --> D
```text

## 5. Solution Architecture

### 5.1. High-Level Architecture
```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Admin App NextJS]
        B[Web App NextJS]
        C[Shared UI Components]
        D[Shared Libraries]
    end
    
    subgraph "API Layer"
        E[Spring Boot REST API]
        F[Authentication Service]
        G[Business Logic Layer]
    end
    
    subgraph "Data Layer"
        H[PostgreSQL]
        I[File Storage]
    end
    
    A --> E
    B --> E
    A --> C
    B --> C
    A --> D
    B --> D
    E --> F
    E --> G
    G --> H
    G --> I
```text

### 5.2. Component Architecture

#### Frontend Components
```text
src/frontend/
├── apps/
│   ├── admin/          # Admin dashboard application
│   └── web/            # Public web application
└── packages/
    ├── ui/             # Shared UI components (Shadcn/UI)
    ├── lib/            # Shared utilities
    ├── types/          # TypeScript type definitions
    ├── api-client/     # API client library
    ├── auth/           # Authentication logic
    └── store/          # Zustand stores
```text

#### Backend Components
```text
src/backend/
├── src/main/java/com/example/
│   ├── controller/     # REST controllers
│   ├── service/        # Business logic services
│   ├── repository/     # Data access layer
│   ├── entity/         # JPA entities
│   ├── dto/            # Data Transfer Objects
│   └── config/         # Configuration classes
```text

### 5.3. Data Flow

#### Typical Request Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend App
    participant A as API Gateway
    participant S as Service Layer
    participant D as Database
    
    U->>F: User Action
    F->>A: HTTP Request
    A->>A: Authentication Check
    A->>S: Business Logic Call
    S->>D: Data Query/Update
    D-->>S: Data Response
    S-->>A: Business Response
    A-->>F: JSON Response
    F-->>U: UI Update
```text

## 6. Technology Stack

### 6.1. Frontend Stack
- **Framework:** NextJS 15.2.3
- **Library:** React 19.0.0
- **Language:** TypeScript 5.7.3
- **Styling:** TailwindCSS 4.0.8
- **UI Components:** Shadcn/UI (Radix UI based)
- **State Management:** Zustand 5.0.6
- **Build Tool:** Turbo
- **Package Manager:** PNPM 10.4.1

### 6.2. Backend Stack
- **Framework:** Spring Boot 1.2.7 ⚠️
- **Language:** Java 21 ⚠️ (compatibility issue)
- **Database:** H2 (dev), PostgreSQL (prod)
- **ORM:** Spring Data JPA
- **Build Tool:** Maven
- **Additional Libraries:** Lombok, Log4JDBC

### 6.3. Infrastructure Stack
- **CI/CD:** GitHub Actions
- **Runtime:** Node.js 20 (frontend), JVM 21 (backend)
- **Database:** PostgreSQL 12+
- **Web Server:** Nginx (reverse proxy)

## 7. Quality Attributes

### 7.1. Performance Requirements
- **Response Time:** < 200ms cho API calls
- **Page Load Time:** < 2s cho first contentful paint
- **Throughput:** 1000 concurrent users
- **Database Query Time:** < 100ms average

### 7.2. Scalability Requirements
- **Horizontal Scaling:** Support load balancer
- **Database Scaling:** Read replicas support
- **File Storage:** Scalable to cloud storage
- **CDN Ready:** Static assets optimization

### 7.3. Security Requirements
- **Authentication:** JWT-based authentication
- **Authorization:** Role-based access control (RBAC)
- **Data Protection:** HTTPS only, input validation
- **Security Headers:** CSP, HSTS, X-Frame-Options

### 7.4. Maintainability
- **Code Quality:** SonarQube integration
- **Testing:** Unit tests >80% coverage
- **Documentation:** Up-to-date architecture docs
- **Monitoring:** Application metrics và logging

## 8. Deployment Architecture

### 8.1. Environment Setup
```mermaid
graph TB
    subgraph "Production Environment"
        A[Load Balancer] --> B[Frontend Server 1]
        A --> C[Frontend Server 2]
        B --> D[Backend API 1]
        C --> E[Backend API 2]
        D --> F[PostgreSQL Master]
        E --> F
        F --> G[PostgreSQL Replica]
    end
    
    subgraph "Development Environment"
        H[Dev Frontend] --> I[Dev Backend]
        I --> J[H2 Database]
    end
```text

### 8.2. CI/CD Pipeline
```mermaid
graph LR
    A[Git Push] --> B[GitHub Actions]
    B --> C[Build & Test]
    C --> D[Security Scan]
    D --> E[Deploy to Staging]
    E --> F[Integration Tests]
    F --> G[Deploy to Production]
```text

### 8.3. Monitoring và Logging
- **Application Metrics:** Micrometer với Prometheus
- **Logging:** Structured logging với ELK stack
- **Error Tracking:** Sentry hoặc similar tool
- **Performance Monitoring:** APM tool

## 9. Risks và Mitigations

### 9.1. Technical Risks

#### High Priority Risk
**Risk:** Spring Boot 1.2.7 incompatibility với Java 21
- **Impact:** Build failures, runtime issues
- **Probability:** High
- **Mitigation:** Upgrade Spring Boot to 3.2+ immediately

**Risk:** Monorepo complexity scaling issues
- **Impact:** Build time increase, deployment complexity
- **Probability:** Medium
- **Mitigation:** Proper caching strategies, incremental builds

### 9.2. Business Risks

**Risk:** Performance không đáp ứng yêu cầu
- **Impact:** User experience kém, business impact
- **Probability:** Medium
- **Mitigation:** Performance testing sớm, optimization strategies

**Risk:** Security vulnerabilities
- **Impact:** Data breach, compliance issues
- **Probability:** Medium
- **Mitigation:** Security reviews, automated security scanning

### 9.3. Operational Risks

**Risk:** Database migration issues
- **Impact:** Data loss, downtime
- **Probability:** Low
- **Mitigation:** Comprehensive backup strategies, migration testing

## 10. Migration và Evolution

### 10.1. Immediate Actions Required
1. **Resolve Java/Spring Boot compatibility**
2. **Setup proper CI/CD pipeline**
3. **Implement basic security measures**
4. **Setup monitoring và logging**

### 10.2. Future Enhancements
- **Microservices migration** (nếu scale requirements tăng)
- **Cloud native deployment** (Kubernetes, Docker)
- **Advanced caching strategies** (Redis, CDN)
- **Real-time features** (WebSocket, Server-Sent Events)