name: 'Requirements Review Analysis'
description: 'Analyzes changes in requirements documents and extracts diff information'

inputs:
  github-token:
    description: 'GitHub token for API access'
    required: true
  event-name:
    description: 'GitHub event name (push or pull_request)'
    required: true
  before-commit:
    description: 'Previous commit SHA for push events'
    required: false
  current-commit:
    description: 'Current commit SHA'
    required: true
  pr-base-sha:
    description: 'PR base commit SHA for pull request events'
    required: false
  pr-head-sha:
    description: 'PR head commit SHA for pull request events'
    required: false

outputs:
  has-changes:
    description: 'Whether there are changes in docs/requirements'
    value: ${{ steps.analyze-changes.outputs.has_changes }}
  changed-files:
    description: 'List of changed files in docs/requirements'
    value: ${{ steps.analyze-changes.outputs.changed_files }}
  diff-content:
    description: 'Diff content for changed files'
    value: ${{ steps.analyze-changes.outputs.diff_content }}
  previous-commit:
    description: 'Previous commit SHA used for comparison'
    value: ${{ steps.analyze-changes.outputs.previous_commit }}
  current-commit:
    description: 'Current commit SHA used for comparison'
    value: ${{ steps.analyze-changes.outputs.current_commit }}

runs:
  using: 'composite'
  steps:
    - name: Analyze requirements changes
      id: analyze-changes
      shell: bash
      run: |
        # Determine commit range based on event type
        if [ "${{ inputs.event-name }}" == "push" ]; then
          PREVIOUS_COMMIT="${{ inputs.before-commit }}"
          CURRENT_COMMIT="${{ inputs.current-commit }}"
        else
          # For merged PR
          PREVIOUS_COMMIT="${{ inputs.pr-base-sha }}"
          CURRENT_COMMIT="${{ inputs.pr-head-sha }}"
        fi
        
        echo "previous_commit=${PREVIOUS_COMMIT}" >> $GITHUB_OUTPUT
        echo "current_commit=${CURRENT_COMMIT}" >> $GITHUB_OUTPUT
        
        # Get list of changed files in docs/requirements (removed head -20 limit)
        CHANGED_FILES=$(git diff --name-only ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/)
        
        if [ -z "$CHANGED_FILES" ]; then
          echo "No changes in docs/requirements directory"
          echo "has_changes=false" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        echo "has_changes=true" >> $GITHUB_OUTPUT
        
        # Format changed files for display
        FORMATTED_FILES=""
        while IFS= read -r file; do
          if [ -n "$file" ]; then
            FORMATTED_FILES="${FORMATTED_FILES}- \`${file}\`\n"
          fi
        done <<< "$CHANGED_FILES"
        
        # Save multiline output
        {
          echo 'changed_files<<EOF'
          echo -e "$FORMATTED_FILES"
          echo 'EOF'
        } >> $GITHUB_OUTPUT
        
        # Get diff for docs/requirements directory (removed head -500 limit)
        DIFF_CONTENT=$(git diff ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/)
        
        # Escape and format diff content for JSON
        DIFF_CONTENT_ESCAPED=$(echo "$DIFF_CONTENT" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')
        
        # Truncate if too long and add notice with link to full diff
        if [ ${#DIFF_CONTENT_ESCAPED} -gt 10000 ]; then
          REPO_URL="${{ github.server_url }}/${{ github.repository }}"
          COMPARE_URL="${REPO_URL}/compare/${PREVIOUS_COMMIT}...${CURRENT_COMMIT}#files_bucket"
          DIFF_CONTENT_ESCAPED="${DIFF_CONTENT_ESCAPED:0:10000}\\n\\n**[Nội dung diff quá dài, đã bị cắt ngắn. Xem chi tiết tại: ${COMPARE_URL}]**"
        fi
        
        {
          echo 'diff_content<<EOF'
          echo -e "$DIFF_CONTENT_ESCAPED"
          echo 'EOF'
        } >> $GITHUB_OUTPUT
